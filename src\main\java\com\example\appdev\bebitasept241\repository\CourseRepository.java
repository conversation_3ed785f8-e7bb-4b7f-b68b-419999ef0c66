package com.example.appdev.bebitasept241.repository;

import com.example.appdev.bebitasept241.entity.Course;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CourseRepository extends JpaRepository<Course, Long> {
    
    // Custom query methods can be added here
    List<Course> findByUnits(Integer units);
    List<Course> findByDescriptionContainingIgnoreCase(String description);
}
