package com.example.appdev.bebitasept241.service;

import com.example.appdev.bebitasept241.entity.Student;
import com.example.appdev.bebitasept241.repository.StudentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class StudentService {
    
    @Autowired
    private StudentRepository studentRepository;
    
    // Get all students
    public List<Student> getAllStudents() {
        return studentRepository.findAll();
    }
    
    // Get student by ID
    public Optional<Student> getStudentById(Long id) {
        return studentRepository.findById(id);
    }
    
    // Save student
    public Student saveStudent(Student student) {
        return studentRepository.save(student);
    }
    
    // Update student
    public Student updateStudent(Long id, Student studentDetails) {
        Optional<Student> optionalStudent = studentRepository.findById(id);
        if (optionalStudent.isPresent()) {
            Student student = optionalStudent.get();
            student.setFirstname(studentDetails.getFirstname());
            student.setLastname(studentDetails.getLastname());
            student.setProgram(studentDetails.getProgram());
            student.setYearlevel(studentDetails.getYearlevel());
            return studentRepository.save(student);
        }
        return null;
    }
    
    // Delete student
    public boolean deleteStudent(Long id) {
        if (studentRepository.existsById(id)) {
            studentRepository.deleteById(id);
            return true;
        }
        return false;
    }
    
    // Get students by program
    public List<Student> getStudentsByProgram(String program) {
        return studentRepository.findByProgram(program);
    }
    
    // Get students by year level
    public List<Student> getStudentsByYearLevel(Integer yearlevel) {
        return studentRepository.findByYearlevel(yearlevel);
    }
    
    // Search students by firstname
    public List<Student> searchStudentsByFirstname(String firstname) {
        return studentRepository.findByFirstnameContainingIgnoreCase(firstname);
    }
    
    // Search students by lastname
    public List<Student> searchStudentsByLastname(String lastname) {
        return studentRepository.findByLastnameContainingIgnoreCase(lastname);
    }
}
