package com.example.appdev.bebitasept241.controller;

import com.example.appdev.bebitasept241.entity.Student;
import com.example.appdev.bebitasept241.service.StudentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/students")
@CrossOrigin(origins = "*")
public class StudentController {
    
    @Autowired
    private StudentService studentService;
    
    // GET all students
    @GetMapping
    public ResponseEntity<List<Student>> getAllStudents() {
        List<Student> students = studentService.getAllStudents();
        return new ResponseEntity<>(students, HttpStatus.OK);
    }
    
    // GET student by ID
    @GetMapping("/{id}")
    public ResponseEntity<Student> getStudentById(@PathVariable Long id) {
        Optional<Student> student = studentService.getStudentById(id);
        if (student.isPresent()) {
            return new ResponseEntity<>(student.get(), HttpStatus.OK);
        }
        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }
    
    // POST create new student
    @PostMapping
    public ResponseEntity<Student> createStudent(@RequestBody Student student) {
        Student savedStudent = studentService.saveStudent(student);
        return new ResponseEntity<>(savedStudent, HttpStatus.CREATED);
    }
    
    // PUT update student
    @PutMapping("/{id}")
    public ResponseEntity<Student> updateStudent(@PathVariable Long id, @RequestBody Student studentDetails) {
        Student updatedStudent = studentService.updateStudent(id, studentDetails);
        if (updatedStudent != null) {
            return new ResponseEntity<>(updatedStudent, HttpStatus.OK);
        }
        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }
    
    // DELETE student
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteStudent(@PathVariable Long id) {
        boolean deleted = studentService.deleteStudent(id);
        if (deleted) {
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        }
        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }
    
    // GET students by program
    @GetMapping("/program/{program}")
    public ResponseEntity<List<Student>> getStudentsByProgram(@PathVariable String program) {
        List<Student> students = studentService.getStudentsByProgram(program);
        return new ResponseEntity<>(students, HttpStatus.OK);
    }
    
    // GET students by year level
    @GetMapping("/yearlevel/{yearlevel}")
    public ResponseEntity<List<Student>> getStudentsByYearLevel(@PathVariable Integer yearlevel) {
        List<Student> students = studentService.getStudentsByYearLevel(yearlevel);
        return new ResponseEntity<>(students, HttpStatus.OK);
    }
    
    // GET students by firstname search
    @GetMapping("/search/firstname/{firstname}")
    public ResponseEntity<List<Student>> searchStudentsByFirstname(@PathVariable String firstname) {
        List<Student> students = studentService.searchStudentsByFirstname(firstname);
        return new ResponseEntity<>(students, HttpStatus.OK);
    }
    
    // GET students by lastname search
    @GetMapping("/search/lastname/{lastname}")
    public ResponseEntity<List<Student>> searchStudentsByLastname(@PathVariable String lastname) {
        List<Student> students = studentService.searchStudentsByLastname(lastname);
        return new ResponseEntity<>(students, HttpStatus.OK);
    }
}
