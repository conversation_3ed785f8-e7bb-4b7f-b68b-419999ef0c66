
#Setup JDBC connection
spring.datasource.url=***********************************

#Setup db login credentials
spring.datasource.username=root
spring.datasource.password=Admin.1234

#Auto create tables based on entities
spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
spring.jpa.hibernate.ddl-auto=update
 
spring.jpa.properties.hibernate.dialect = org.hibernate.dialect.MySQLDialect

#for stack trace error not to appear 
server.error.include-stacktrace=never

#server.port = 8080

security.basic.enabled=false